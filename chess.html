<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#050A24">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <title>Chess Game</title>
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>♞</text></svg>">
    <!-- jQuery required for chessboard.js -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Load chess.js library for chess logic -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chess.js/0.10.3/chess.min.js"></script>
    <!-- Load chessboard.js and its CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- PeerJS for P2P multiplayer -->
    <script src="https://unpkg.com/peerjs@1.5.0/dist/peerjs.min.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="chess.css">
    
    <!-- Mobile touch detection script -->
    <script>
        // Detect if device is a touch device
        window.isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        // Prevent page scroll when interacting with the board
        document.addEventListener('touchmove', function(e) {
            if (e.target.closest('#game-board') || e.target.closest('#mp-game-board') ||
                e.target.closest('.piece-417db')) {
                e.preventDefault();
            }
        }, {passive: false});
    </script>
    
    <!-- Override styles for chessboard.js drag behavior -->
    <style>
        /* Ensure dragged pieces are always visible */
        img.piece-417db {
            opacity: 1 !important;
            cursor: pointer;
            touch-action: manipulation;
        }

        /* Fix for ghost pieces during drag */
        body > img[src*="chesspieces"] {
            opacity: 1 !important;
            transform: scale(1.15);
            filter: drop-shadow(3px 6px 8px rgba(0, 0, 0, 0.7));
            pointer-events: none;
            z-index: 9999;
        }

        /* Make squares more touch-friendly */
        .square-55d63 {
            cursor: pointer;
            touch-action: manipulation;
        }

        /* Improve piece visibility on mobile */
        @media (max-width: 768px) {
            img.piece-417db {
                transform: scale(1.05);
                transition: transform 0.1s ease;
            }

            img.piece-417db:active {
                transform: scale(1.1);
            }
        }
    </style>
</head>
<body>
    <main class="main-container">
        <h1 class="game-title">Chess</h1>

        <!-- Game Mode Selection -->
        <div id="mode-selector" class="mode-selector">
            <h2 class="mode-title">Play Chess Online</h2>
            <p class="mode-description">Create a room or join an existing game</p>

            <div class="setup-buttons">
                <button id="create-game-btn" class="game-btn primary-btn">
                    <i class="fas fa-plus"></i>
                    Create a Game
                </button>
                <button id="join-game-btn" class="game-btn secondary-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    Join a Game
                </button>
            </div>
        </div>

        <!-- Create Game Section -->
        <div id="create-game-section" class="create-game-section" style="display: none;">
            <div class="section-header">
                <button class="back-btn" onclick="goBackToModeSelection()" title="Back">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h2 class="section-title">Share this code with your opponent</h2>
            </div>

            <div class="connection-field">
                <div class="game-code">
                    <input id="game-code-display" class="code-input" type="text" readonly>
                    <button id="copy-code-btn" class="game-btn primary-btn">
                        <i class="fas fa-copy"></i> Copy Code
                    </button>
                </div>
            </div>
            <div id="create-status-text" class="connection-status">Generating game code...</div>
        </div>

        <!-- Join Game Section -->
        <div id="join-game-section" class="join-game-section" style="display: none;">
            <div class="section-header">
                <button class="back-btn" onclick="goBackToModeSelection()" title="Back">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h2 class="section-title">Enter the game code</h2>
            </div>

            <div class="connection-field">
                <div class="game-code">
                    <input id="game-code-input" class="code-input" type="text" placeholder="ENTER 6-DIGIT CODE" maxlength="6">
                    <button id="connect-btn" class="game-btn primary-btn">
                        <i class="fas fa-link"></i> Connect
                    </button>
                </div>
            </div>
            <div id="join-status-text" class="connection-status">Enter the code provided by your opponent</div>
        </div>


        


    </main>

    <!-- Game Over Popup -->
    <div id="game-over-popup" class="popup-overlay">
        <div class="popup-content">
            <div class="popup-body">
                <div class="result-icon">
                    <i id="result-icon" class="fas fa-crown"></i>
                </div>
                <h2 id="game-result-title">Game Over</h2>
                <p id="game-result-message">Checkmate! White wins!</p>
                <div class="game-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Moves:</span>
                        <span id="total-moves">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Game Duration:</span>
                        <span id="game-duration">00:00</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Result:</span>
                        <span id="final-result">Checkmate</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotion Popup -->
    <div id="promotion-popup" class="popup-overlay">
        <div class="popup-content promotion-content">
            <div class="popup-body">
                <h2>Choose Promotion</h2>
                <p>Select a piece to promote your pawn:</p>
                <div class="promotion-options">
                    <div class="promotion-piece" data-piece="q">
                        <div class="piece-icon"></div>
                        <span>Queen</span>
                    </div>
                    <div class="promotion-piece" data-piece="r">
                        <div class="piece-icon"></div>
                        <span>Rook</span>
                    </div>
                    <div class="promotion-piece" data-piece="b">
                        <div class="piece-icon"></div>
                        <span>Bishop</span>
                    </div>
                    <div class="promotion-piece" data-piece="n">
                        <div class="piece-icon"></div>
                        <span>Knight</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mode Selection Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get DOM elements
            const modeSelector = document.getElementById('mode-selector');
            const createGameSection = document.getElementById('create-game-section');
            const joinGameSection = document.getElementById('join-game-section');
            const createGameBtn = document.getElementById('create-game-btn');
            const joinGameBtn = document.getElementById('join-game-btn');
            const gameCodeDisplay = document.getElementById('game-code-display');
            const copyCodeBtn = document.getElementById('copy-code-btn');
            const gameCodeInput = document.getElementById('game-code-input');
            const connectBtn = document.getElementById('connect-btn');
            const createStatusText = document.getElementById('create-status-text');
            const joinStatusText = document.getElementById('join-status-text');

            // Event listeners
            createGameBtn.addEventListener('click', showCreateGameSection);
            joinGameBtn.addEventListener('click', showJoinGameSection);
            copyCodeBtn.addEventListener('click', copyGameCode);
            connectBtn.addEventListener('click', connectToGame);

            // Format input to uppercase and limit to 6 characters
            gameCodeInput.addEventListener('input', function(e) {
                e.target.value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 6);
            });

            function showCreateGameSection() {
                modeSelector.style.display = 'none';
                createGameSection.style.display = 'block';
                generateGameCode();
            }

            function showJoinGameSection() {
                modeSelector.style.display = 'none';
                joinGameSection.style.display = 'block';
                gameCodeInput.focus();
            }

            function generateGameCode() {
                // Generate a 6-character alphanumeric code
                const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                let code = '';
                for (let i = 0; i < 6; i++) {
                    code += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                gameCodeDisplay.value = code;
                createStatusText.textContent = 'Initializing peer connection...';

                // Initialize PeerJS connection
                initializePeerConnection(code, true);
            }

            function copyGameCode() {
                gameCodeDisplay.select();
                gameCodeDisplay.setSelectionRange(0, 99999); // For mobile devices
                navigator.clipboard.writeText(gameCodeDisplay.value).then(function() {
                    copyCodeBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    setTimeout(() => {
                        copyCodeBtn.innerHTML = '<i class="fas fa-copy"></i> Copy Code';
                    }, 2000);
                });
            }

            function connectToGame() {
                const code = gameCodeInput.value.trim();
                if (code.length === 6) {
                    joinStatusText.textContent = `Connecting to game ${code}...`;
                    connectBtn.disabled = true;

                    // Initialize PeerJS connection to join game
                    initializePeerConnection(code, false);
                } else {
                    joinStatusText.textContent = 'Please enter a valid 6-character game code';
                    joinStatusText.style.color = '#e74c3c';
                    setTimeout(() => {
                        joinStatusText.style.color = '';
                        joinStatusText.textContent = 'Enter the code provided by your opponent';
                    }, 3000);
                }
            }
        });

        // PeerJS variables
        let peer = null;
        let connection = null;
        let isHost = false;

        // Initialize PeerJS connection
        function initializePeerConnection(roomCode, isCreating) {
            try {
                // Create peer with room-specific ID
                const peerId = isCreating ? `host_${roomCode}` : `guest_${Date.now()}`;
                peer = new Peer(peerId);

                peer.on('open', (id) => {
                    console.log('Peer connected with ID:', id);

                    if (isCreating) {
                        isHost = true;
                        createStatusText.textContent = 'Waiting for opponent to join...';

                        // Listen for incoming connections
                        peer.on('connection', (conn) => {
                            connection = conn;
                            setupConnection();
                            createStatusText.textContent = 'Player joined! Starting game...';
                            setTimeout(() => startGame(), 1000);
                        });
                    } else {
                        // Connect to host
                        const hostId = `host_${roomCode}`;
                        connection = peer.connect(hostId);
                        setupConnection();
                    }
                });

                peer.on('error', (error) => {
                    console.error('Peer error:', error);
                    if (isCreating) {
                        createStatusText.textContent = 'Error creating game. Please try again.';
                    } else {
                        joinStatusText.textContent = 'Could not connect to game. Check the code and try again.';
                        connectBtn.disabled = false;
                    }
                });

            } catch (error) {
                console.error('Failed to initialize peer:', error);
                if (isCreating) {
                    createStatusText.textContent = 'Failed to create game. Please try again.';
                } else {
                    joinStatusText.textContent = 'Connection failed. Please try again.';
                    connectBtn.disabled = false;
                }
            }
        }

        function setupConnection() {
            if (!connection) return;

            connection.on('open', () => {
                console.log('Connection established');
                if (!isHost) {
                    joinStatusText.textContent = 'Connected! Starting game...';
                    setTimeout(() => startGame(), 1000);
                }
            });

            connection.on('data', (data) => {
                console.log('Received data:', data);
                // Handle game moves and other data here
            });

            connection.on('close', () => {
                console.log('Connection closed');
                // Handle disconnection
            });

            connection.on('error', (error) => {
                console.error('Connection error:', error);
            });
        }

        function startGame() {
            // Hide all setup sections and show game
            document.getElementById('mode-selector').style.display = 'none';
            document.getElementById('create-game-section').style.display = 'none';
            document.getElementById('join-game-section').style.display = 'none';

            // Here you would initialize the actual chess game
            alert(`Game starting! You are ${isHost ? 'White (Host)' : 'Black (Guest)'}`);
        }

        // Back button functionality
        function goBackToModeSelection() {
            document.getElementById('mode-selector').style.display = 'block';
            document.getElementById('create-game-section').style.display = 'none';
            document.getElementById('join-game-section').style.display = 'none';

            // Clean up peer connections
            if (connection) {
                connection.close();
                connection = null;
            }
            if (peer) {
                peer.destroy();
                peer = null;
            }

            // Reset forms
            document.getElementById('game-code-input').value = '';
            document.getElementById('connect-btn').disabled = false;
            document.getElementById('join-status-text').textContent = 'Enter the code provided by your opponent';
            isHost = false;
        }
    </script>

    <!-- Custom Scripts -->
    <!-- <script src="p2p-chess.js"></script> -->
    <script src="chess.js"></script>
</body>
</html>

