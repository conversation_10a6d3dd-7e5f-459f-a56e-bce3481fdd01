<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#050A24">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <title>Chess Game</title>
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>♞</text></svg>">
    <!-- jQuery required for chessboard.js -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Load chess.js library for chess logic -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chess.js/0.10.3/chess.min.js"></script>
    <!-- Load chessboard.js and its CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- PeerJS for P2P multiplayer -->
    <script src="https://unpkg.com/peerjs@1.5.0/dist/peerjs.min.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="chess.css">
    
    <!-- Mobile touch detection script -->
    <script>
        // Detect if device is a touch device
        window.isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        // Prevent page scroll when interacting with the board
        document.addEventListener('touchmove', function(e) {
            if (e.target.closest('#game-board') || e.target.closest('#mp-game-board') ||
                e.target.closest('.piece-417db')) {
                e.preventDefault();
            }
        }, {passive: false});
    </script>
    
    <!-- Override styles for chessboard.js drag behavior -->
    <style>
        /* Ensure dragged pieces are always visible */
        img.piece-417db {
            opacity: 1 !important;
            cursor: pointer;
            touch-action: manipulation;
        }

        /* Fix for ghost pieces during drag */
        body > img[src*="chesspieces"] {
            opacity: 1 !important;
            transform: scale(1.15);
            filter: drop-shadow(3px 6px 8px rgba(0, 0, 0, 0.7));
            pointer-events: none;
            z-index: 9999;
        }

        /* Make squares more touch-friendly */
        .square-55d63 {
            cursor: pointer;
            touch-action: manipulation;
        }

        /* Improve piece visibility on mobile */
        @media (max-width: 768px) {
            img.piece-417db {
                transform: scale(1.05);
                transition: transform 0.1s ease;
            }

            img.piece-417db:active {
                transform: scale(1.1);
            }
        }
    </style>
</head>
<body>
    <main class="main-container">
        <h1 class="game-title">Chess</h1>

        <!-- Game Mode Selection -->
        <div id="mode-selector" class="mode-selector">
            <h2 class="mode-title">Choose Game Mode</h2>
            <p class="mode-description">Select how you want to play chess</p>

            <div class="mode-options">
                <div class="mode-card" id="single-player-card">
                    <div class="mode-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="mode-name">Single Player</h3>
                    <p class="mode-desc">Play against AI with adjustable difficulty levels</p>
                    <div class="mode-features">
                        <span class="feature"><i class="fas fa-brain"></i> AI Opponent</span>
                        <span class="feature"><i class="fas fa-cog"></i> Difficulty Settings</span>
                        <span class="feature"><i class="fas fa-palette"></i> Choose Color</span>
                    </div>
                    <button class="mode-btn primary" onclick="selectSinglePlayer()">
                        <i class="fas fa-play"></i>
                        Play vs AI
                    </button>
                </div>

                <div class="mode-card" id="multiplayer-card">
                    <div class="mode-icon">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <h3 class="mode-name">Multiplayer</h3>
                    <p class="mode-desc">Play against friends online in real-time</p>
                    <div class="mode-features">
                        <span class="feature"><i class="fas fa-globe"></i> Online Play</span>
                        <span class="feature"><i class="fas fa-key"></i> Room Codes</span>
                        <span class="feature"><i class="fas fa-clock"></i> Real-time</span>
                    </div>
                    <button class="mode-btn secondary" onclick="selectMultiplayer()">
                        <i class="fas fa-wifi"></i>
                        Play Online
                    </button>
                </div>
            </div>
        </div>

        <!-- P2P Connection -->
        <div id="p2p-lobby" class="p2p-lobby" style="display: none;">
            <div class="lobby-header">
                <button class="back-btn" onclick="goBackToModeSelection()" title="Back to Game Modes">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h2 class="lobby-title">Multiplayer Chess</h2>
            </div>

            <div class="connection-status">
                <div id="peer-status" class="peer-status">
                    <i class="fas fa-circle status-indicator"></i>
                    <span id="status-text">Initializing...</span>
                </div>
                <div id="room-code-display" class="room-code-display" style="display: none;">
                    Room Code: <span id="room-code">------</span>
                    <button id="copy-code-btn" class="copy-btn" title="Copy Room Code">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>

            <div class="lobby-options">
                <div class="option-section">
                    <h3>Create Game</h3>
                    <p>Generate a 6-digit room code to share with a friend</p>
                    <button id="create-room-btn" class="action-btn primary">
                        <i class="fas fa-plus"></i>
                        Create Room
                    </button>
                </div>

                <div class="option-divider">OR</div>

                <div class="option-section">
                    <h3>Join Game</h3>
                    <p>Enter the 6-digit room code to join a game</p>
                    <div class="join-form">
                        <input type="text" id="room-code-input" placeholder="Enter 6-digit room code" maxlength="6" pattern="[0-9]{6}">
                        <button id="join-room-btn" class="action-btn secondary">
                            <i class="fas fa-sign-in-alt"></i>
                            Join Game
                        </button>
                    </div>
                </div>
            </div>

            <div id="connection-progress" class="connection-progress" style="display: none;">
                <div class="loading-spinner"></div>
                <div id="connection-message" class="connection-message">Connecting...</div>
                <button id="cancel-connection-btn" class="action-btn cancel">Cancel</button>
            </div>
        </div>
        


    </main>

    <!-- Game Over Popup -->
    <div id="game-over-popup" class="popup-overlay">
        <div class="popup-content">
            <div class="popup-body">
                <div class="result-icon">
                    <i id="result-icon" class="fas fa-crown"></i>
                </div>
                <h2 id="game-result-title">Game Over</h2>
                <p id="game-result-message">Checkmate! White wins!</p>
                <div class="game-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Moves:</span>
                        <span id="total-moves">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Game Duration:</span>
                        <span id="game-duration">00:00</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Result:</span>
                        <span id="final-result">Checkmate</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotion Popup -->
    <div id="promotion-popup" class="popup-overlay">
        <div class="popup-content promotion-content">
            <div class="popup-body">
                <h2>Choose Promotion</h2>
                <p>Select a piece to promote your pawn:</p>
                <div class="promotion-options">
                    <div class="promotion-piece" data-piece="q">
                        <div class="piece-icon"></div>
                        <span>Queen</span>
                    </div>
                    <div class="promotion-piece" data-piece="r">
                        <div class="piece-icon"></div>
                        <span>Rook</span>
                    </div>
                    <div class="promotion-piece" data-piece="b">
                        <div class="piece-icon"></div>
                        <span>Bishop</span>
                    </div>
                    <div class="promotion-piece" data-piece="n">
                        <div class="piece-icon"></div>
                        <span>Knight</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mode Selection Script -->
    <script>
        function selectSinglePlayer() {
            // Redirect to the single player chess game
            window.location.href = 'chess/chess.html?start=single';
        }

        function selectMultiplayer() {
            // Hide mode selector and show P2P lobby
            document.getElementById('mode-selector').style.display = 'none';
            document.getElementById('p2p-lobby').style.display = 'block';
        }

        // Add back button functionality for P2P lobby
        function goBackToModeSelection() {
            document.getElementById('p2p-lobby').style.display = 'none';
            document.getElementById('mode-selector').style.display = 'block';
        }
    </script>

    <!-- Custom Scripts -->
    <script src="p2p-chess.js"></script>
    <script src="chess.js"></script>
</body>
</html>

